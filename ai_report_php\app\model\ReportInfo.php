<?php
namespace app\model;

use think\Model;
use support\Log;
use support\think\Db;

class ReportInfo extends Model
{
    protected $table = 'ba_report_info';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 根据报告ID获取推荐院校列表
     *
     * @param int $reportId 报告ID
     * @return array
     */
    public static function getRecommendationsByReportId($reportId)
    {
        return self::where('report_id', $reportId)
            ->where('status', 1)
            ->order('id', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 根据学生ID获取推荐院校列表
     *
     * @param int $studentId 学生ID
     * @return array
     */
    public static function getRecommendationsByStudentId($studentId)
    {
        return self::where('student_id', $studentId)
            ->where('status', 1)
            ->order('id', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 删除报告的推荐数据
     *
     * @param int $reportId 报告ID
     * @return bool
     */
    public static function deleteByReportId($reportId)
    {
        return self::where('report_id', $reportId)->delete();
    }

    /**
     * 批量插入推荐院校数据
     *
     * @param array $data 推荐院校数据数组
     * @return bool
     */
    public static function insertRecommendations($data)
    {
        if (empty($data)) {
            return false;
        }

        return self::insertAll($data);
    }

    /**
     * 获取推荐院校统计信息
     *
     * @param int $reportId 报告ID
     * @return array
     */
    public static function getRecommendationStats($reportId)
    {
        $total = self::where('report_id', $reportId)
            ->where('status', 1)
            ->count();

        $withDifficulty = self::where('report_id', $reportId)
            ->where('status', 1)
            ->where('competition_difficulty', '<>', '')
            ->count();

        $withSuggestions = self::where('report_id', $reportId)
            ->where('status', 1)
            ->where('suggestions', '<>', '')
            ->count();

        return [
            'total' => $total,
            'with_difficulty' => $withDifficulty,
            'with_suggestions' => $withSuggestions
        ];
    }


     /**
     * 从推荐学校列表中提取学校列表数据
     *
     * @param array $recommendedSchools AI推荐的学校数据
     * @param int $reportId 报告ID
     * @return array
     */
    public function extractSchoolListFromRecommendations($recommendedSchools, $reportId)
    {
        try {
            // 获取报告信息以便计算分差
            $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
            if (empty($report)) {
                Log::error('未找到报告信息，reportId: ' . $reportId);
                return [];
            }

            $totalScore = intval($report['total_score']);
            $majorCode = $report['major_code'];

           // Log::info('提取学校列表 - 报告ID: ' . $reportId . ', 总分: ' . $totalScore . ', 专业代码: ' . $majorCode);

            $schoolList = [];
            $schoolNames = []; // 用于记录已添加的学校名称，避免重复
            $index = 1;

            // 合并推荐列表和高推荐列表
            $allRecommendations = [];
            if (isset($recommendedSchools['recommend_list']) && is_array($recommendedSchools['recommend_list'])) {
                $allRecommendations = array_merge($allRecommendations, $recommendedSchools['recommend_list']);
                //Log::info('recommend_list 学校数量: ' . count($recommendedSchools['recommend_list']));
            }
            if (isset($recommendedSchools['high_recommend_list']) && is_array($recommendedSchools['high_recommend_list'])) {
                array_push($allRecommendations, $recommendedSchools['high_recommend_list']);
                //Log::info('high_recommend_list 学校数量: ' . count($recommendedSchools['high_recommend_list']));
            }

            //Log::info('合并后总学校数量: ' . count($allRecommendations));
            //Log::info('所有推荐学校名称: ' . json_encode(array_column($allRecommendations, 'school_name'), JSON_UNESCAPED_UNICODE));

            foreach ($allRecommendations as $school) {
                $schoolName = $school['school_name']??'';
                //Log::info('处理学校: ' . $schoolName);

                // 检查学校是否已经添加过（避免重复）
                if (in_array($schoolName, $schoolNames)) {
                    Log::info('学校已存在，跳过: ' . $schoolName);
                    continue; // 如果已添加过，跳过当前学校
                }

                // 从数据库查询学校详细信息
                //Log::info('查询数据库 - 学校: ' . $schoolName . ', 专业代码: ' . $majorCode);
                //按专业和名称搜索
                $schoolInfo = SchoolInfo::where('school_name', $schoolName)
                ->where('major_code',$majorCode)
                    ->find();

                // 添加SQL调试
               // $lastSql = SchoolInfo::getLastSql();

                if ($schoolInfo) {
                    // 如果是对象，转换为数组
                    if (is_object($schoolInfo) && method_exists($schoolInfo, 'toArray')) {
                        $schoolInfoData = $schoolInfo->toArray();
                    } elseif (is_array($schoolInfo)) {
                        $schoolInfoData = $schoolInfo;
                    } else {
                       
                        continue; // 跳过无效数据
                    }

                    // 计算分差
                    $scoreDiff = $totalScore - $schoolInfoData['must_reach_score'];

                    // 确定地区
                    $region = $this->getRegionByProvince($schoolInfoData['province']);

                    // 确定城市（从省份中提取或使用area字段）
                    $city = $schoolInfoData['area'] ?? $schoolInfoData['province'];

                    // 处理学院信息
                    $college = $schoolInfoData['college'] ?? '信息科学与技术学院';

                    // 处理学校标签
                    $tags = [];
                    if (strpos($schoolInfoData['school_type'], '985') !== false) {
                        $tags[] = '985';
                    }
                    if (strpos($schoolInfoData['school_type'], '211') !== false) {
                        $tags[] = '211';
                    }
                    if (strpos($schoolInfoData['school_type'], '双一流') !== false) {
                        $tags[] = '双一流';
                    }

                    // 添加学校到列表
                    $schoolList[] = [
                        'id' => $index,
                        'school_name' => $schoolInfoData['school_name'],
                        'region' => $region,
                        'city' => $city,
                        'college' => $college,
                        'major_name' => $schoolInfoData['major_name'],
                        'major_code' => $schoolInfoData['major_code'] ?? $majorCode,
                        'min_score' => $schoolInfoData['must_reach_score'],
                        'score_diff' => $scoreDiff,
                        'tags' => $tags,
                        'tag_985' => in_array('985', $tags),
                        'tag_211' => in_array('211', $tags),
                        'tag_double' => in_array('双一流', $tags)
                    ];

                   // Log::info('成功添加学校到列表: ' . $schoolName . ', 分数线: ' . $schoolInfoData['must_reach_score']);
                } else {
                    // 如果数据库中没有找到，使用推荐数据创建基本信息
                    // 保持学校在列表中，确保数量一致
                    Log::warning('数据库中未找到学校，使用基本信息: ' . $schoolName);
                    $schoolList[] = [
                        'id' => $index,
                        'school_name' => $schoolName,
                        'region' => '未知',
                        'city' => '未知',
                        'college' => '未知',
                        'major_name' => $school['major_name'] ?? '未知',
                        'major_code' => $majorCode,
                        'min_score' => 0,
                        'score_diff' => 0,
                        'tags' => [],
                        'tag_985' => false,
                        'tag_211' => false,
                        'tag_double' => false
                    ];

                   // Log::info('使用基本信息添加学校: ' . $schoolName);
                }

                // 记录已添加的学校名称
                $schoolNames[] = $schoolName;
                $index++;
                //Log::info('当前已处理学校数量: ' . count($schoolList));
            }

            Log::info('最终学校列表数量: ' . count($schoolList));
            Log::info('最终学校列表名称: ' . json_encode(array_column($schoolList, 'school_name'), JSON_UNESCAPED_UNICODE));

            return $schoolList;

        } catch (\Exception $e) {
            Log::error('从推荐学校提取列表数据异常: ' . $e->getMessage());
            Log::error('异常堆栈: ' . $e->getTraceAsString());
            // 出现异常时，返回空数组
            return [];
        }
    }

     /**
     * 根据省份获取地区
     *
     * @param string $province
     * @return string
     */
    private function getRegionByProvince($province)
    {
        $regionMap = [
            '华北' => ['北京市', '天津市', '河北省', '山西省', '内蒙古自治区'],
            '东北' => ['辽宁省', '吉林省', '黑龙江省'],
            '华东' => ['上海市', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省'],
            '华中' => ['河南省', '湖北省', '湖南省'],
            '华南' => ['广东省', '广西壮族自治区', '海南省'],
            '西南' => ['重庆市', '四川省', '贵州省', '云南省', '西藏自治区'],
            '西北' => ['陕西省', '甘肃省', '青海省', '宁夏回族自治区', '新疆维吾尔自治区']
        ];

        foreach ($regionMap as $region => $provinces) {
            if (in_array($province, $provinces)) {
                return $region;
            }
        }

        return '其他';
    }

    
       /**
     * 整合学校数据库信息
     *
     * @param array $school 学校基本信息
     * @return array 整合后的学校信息
     */
    public function enrichSchoolData($school)
    {
        try {
            $schoolId = $school['school_id'];
            $schoolName = $school['school_name'];

            // 1. 查询拟录取名单统计
            $admissionStats = $this->getAdmissionStats($schoolId);

            // 2. 查询复试名单统计
            $retestStats = $this->getRetestStats($schoolId);

            // 3. 查询院校基本信息
            $schoolBasicInfo = $this->getSchoolBasicInfo($schoolId);

            // 4. 查询学校信息（logo及标签）
            $schoolInfo = $this->getSchoolInfo($schoolName);

            // 5. 获取本年度复试名单列表
            $currentYearRetestList = $this->getCurrentYearRetestList($schoolId);

            // 6. 获取本年度拟录取名单列表
            $currentYearAdmissionList = $this->getCurrentYearAdmissionList($schoolId);

            // 整合数据
            $school['admission_stats'] = $admissionStats;
            $school['retest_stats'] = $retestStats;
            $school['basic_info'] = $schoolBasicInfo;
            $school['school_info'] = $schoolInfo;
            $school['current_year_retest_list'] = $currentYearRetestList;
            $school['current_year_admission_list'] = $currentYearAdmissionList;

            return $school;

        } catch (\Exception $e) {
            Log::error('整合学校数据异常: ' . $e->getMessage());
            return $school; // 异常时返回原始数据
        }
    }

    /**
     * 获取拟录取名单统计
     *
     * @param int $schoolId
     * @return array
     */
    private function getAdmissionStats($schoolId)
    {
        try {
            // 使用原生SQL查询，获取拟录取统计信息
            $sql = "SELECT
                        COUNT(*) as total_admission,
                        AVG(CAST(initial_score AS DECIMAL(10,2))) as avg_initial_score,
                        MAX(CAST(initial_score AS DECIMAL(10,2))) as max_initial_score,
                        MIN(CAST(initial_score AS DECIMAL(10,2))) as min_initial_score,
                        AVG(CAST(retest_score AS DECIMAL(10,2))) as avg_retest_score,
                        MAX(CAST(retest_score AS DECIMAL(10,2))) as max_retest_score,
                        MIN(CAST(retest_score AS DECIMAL(10,2))) as min_retest_score,
                        year
                    FROM ba_admission_list
                    WHERE school_id = $schoolId
                    GROUP BY year
                    ORDER BY year DESC
                    LIMIT 3";

            $result = Db::query($sql);

            return [
                'yearly_stats' => $result ?: [],
                'has_data' => !empty($result)
            ];

        } catch (\Exception $e) {
            Log::error('获取拟录取统计异常: ' . $e->getMessage());
            return ['yearly_stats' => [], 'has_data' => false];
        }
    }

    /**
     * 获取复试名单统计
     *
     * @param int $schoolId
     * @return array
     */
    private function getRetestStats($schoolId)
    {
        try {
            // 使用原生SQL查询，获取复试统计信息
            $sql = "SELECT
                        COUNT(*) as total_retest,
                        AVG(initial_score) as avg_initial_score,
                        MAX(initial_score) as max_initial_score,
                        MIN(initial_score) as min_initial_score,
                        COUNT(CASE WHEN admission_status = '拟录取' THEN 1 END) as admission_count,
                        year
                    FROM ba_retest_list
                    WHERE school_id = $schoolId
                    GROUP BY year
                    ORDER BY year DESC
                    LIMIT 3";

            $result = Db::query($sql);

            // 计算录取率
            foreach ($result as &$item) {
                if ($item['total_retest'] > 0) {
                    $item['admission_rate'] = round(($item['admission_count'] / $item['total_retest']) * 100, 2);
                } else {
                    $item['admission_rate'] = 0;
                }
            }

            return [
                'yearly_stats' => $result ?: [],
                'has_data' => !empty($result)
            ];

        } catch (\Exception $e) {
            Log::error('获取复试统计异常: ' . $e->getMessage());
            return ['yearly_stats' => [], 'has_data' => false];
        }
    }

    /**
     * 获取院校基本信息
     *
     * @param int $schoolId
     * @return array
     */
    private function getSchoolBasicInfo($schoolId)
    {
        try {
            $result = Db::name('school_basic_info')
                ->where('school_id', $schoolId)
                ->field('research_direction,exam_range,reference_books,retest_content,tuition_fee,study_years,accommodation,admission_requirements')
                ->find();

            if ($result && is_object($result) && method_exists($result, 'toArray')) {
               // Log::info("找到学校基本信息 (ID: {$schoolId}): " . json_encode($result->toArray(), JSON_UNESCAPED_UNICODE));
                return $result->toArray();
            } elseif (is_array($result)) {
               // Log::info("找到学校基本信息 (ID: {$schoolId}): " . json_encode($result, JSON_UNESCAPED_UNICODE));
                return $result;
            } else {
                Log::warning("未找到学校基本信息 (ID: {$schoolId})");
                return [];
            }

        } catch (\Exception $e) {
            Log::error('获取院校基本信息异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取学校信息（logo及标签）
     *
     * @param string $schoolName
     * @return array
     */
    private function getSchoolInfo($schoolName)
    {
        try {
            $result = Db::name('school')
                ->where('name', $schoolName)
                ->where('is_delete', 0)
                ->field('name, id,logo,tag_211,tag_985,dual_class,provice_city as address,phone,home_site,zsb_site')
                ->find();

            if ($result) {
                Log::info("找到学校数据: {$result['name']}, ID: {$result['id']}, logo: {$result['logo']}");
            } else {
                Log::warning("未找到学校信息: {$schoolName}");
                // 尝试模糊匹配
                $fuzzyResult = Db::name('school')
                    ->where('name', 'like', "%{$schoolName}%")
                    ->where('is_delete', 0)
                    ->field('name, id,logo,tag_211,tag_985,dual_class,address,phone,home_site,zsb_site')
                    ->find();

                if ($fuzzyResult) {
                    Log::info("通过模糊匹配找到学校: {$fuzzyResult['name']} (搜索: {$schoolName})");
                    $result = $fuzzyResult;
                } else {
                    Log::error("完全未找到学校信息: {$schoolName}");
                    return [];
                }
            }

            if ($result) {
                // 检查是否为对象，如果是则转换为数组
                if (is_object($result) && method_exists($result, 'toArray')) {
                    $schoolData = $result->toArray();
                } elseif (is_array($result)) {
                    $schoolData = $result;
                } else {
                    return [];
                }

                // 处理logo
                if (empty($schoolData['logo'])) {

                    $schoolData['logo'] = "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/{$schoolData['id']}.jpeg";
                }

                // 转换标签为布尔值
                $schoolData['is_211'] = (bool)$schoolData['tag_211'];
                $schoolData['is_985'] = (bool)$schoolData['tag_985'];
                $schoolData['is_dual_class'] = (bool)$schoolData['dual_class'];

                return $schoolData;
            }

            return [];

        } catch (\Exception $e) {
            Log::error('获取学校信息异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取本年度复试名单列表
     *
     * @param int $schoolId
     * @return array
     */
    private function getCurrentYearRetestList($schoolId)
    {
        try {
            // 首先尝试获取当前年度的数据
            $currentYear = intval(date('Y'));

            $resultCollection = Db::name('retest_list')
                ->where('school_id', $schoolId)
                ->where('year', $currentYear)
                ->field('name,college,major_code,major_name,politics_score,english_score,major1_score,major2_score,initial_score,volunteer_type,admission_status,year')
                ->order('initial_score', 'desc')
                ->limit(20)
                ->select();

            $result = [];
            if ($resultCollection && method_exists($resultCollection, 'toArray')) {
                $result = $resultCollection->toArray();
            } elseif (is_array($resultCollection)) {
                $result = $resultCollection;
            }

            // 如果当前年度没有数据，获取最新年度的数据
            if (empty($result)) {
                $maxYearResult = Db::name('retest_list')
                    ->where('school_id', $schoolId)
                    ->max('year');

                if ($maxYearResult) {
                    $resultCollection = Db::name('retest_list')
                        ->where('school_id', $schoolId)
                        ->where('year', $maxYearResult)
                        ->field('name,college,major_code,major_name,politics_score,english_score,major1_score,major2_score,initial_score,volunteer_type,admission_status,year')
                        ->order('initial_score', 'desc')
                        ->limit(20)
                        ->select();

                    if ($resultCollection && method_exists($resultCollection, 'toArray')) {
                        $result = $resultCollection->toArray();
                    } elseif (is_array($resultCollection)) {
                        $result = $resultCollection;
                    }
                }
            }

            return [
                'list' => $result ?: [],
                'year' => !empty($result) ? $result[0]['year'] : null,
                'count' => count($result ?: [])
            ];

        } catch (\Exception $e) {
            Log::error('获取本年度复试名单列表异常: ' . $e->getMessage());
            return ['list' => [], 'year' => null, 'count' => 0];
        }
    }

    /**
     * 获取本年度拟录取名单列表
     *
     * @param int $schoolId
     * @return array
     */
    private function getCurrentYearAdmissionList($schoolId)
    {
        try {
            // 首先尝试获取当前年度的数据
            $currentYear = intval(date('Y'));

            $resultCollection = Db::name('admission_list')
                ->where('school_id', $schoolId)
                ->where('year', $currentYear)
                ->field('name,college,major_code,major_name,initial_score,retest_score,total_score,first_choice_school,student_remark,year')
                ->order('total_score', 'desc')
                ->limit(20)
                ->select();

            $result = [];
            if ($resultCollection && method_exists($resultCollection, 'toArray')) {
                $result = $resultCollection->toArray();
            } elseif (is_array($resultCollection)) {
                $result = $resultCollection;
            }

            // 如果当前年度没有数据，获取最新年度的数据
            if (empty($result)) {
                $maxYearResult = Db::name('admission_list')
                    ->where('school_id', $schoolId)
                    ->max('year');

                if ($maxYearResult) {
                    $resultCollection = Db::name('admission_list')
                        ->where('school_id', $schoolId)
                        ->where('year', $maxYearResult)
                        ->field('name,college,major_code,major_name,initial_score,retest_score,total_score,first_choice_school,student_remark,year')
                        ->order('total_score', 'desc')
                        ->limit(20)
                        ->select();

                    if ($resultCollection && method_exists($resultCollection, 'toArray')) {
                        $result = $resultCollection->toArray();
                    } elseif (is_array($resultCollection)) {
                        $result = $resultCollection;
                    }
                }
            }

            return [
                'list' => $result ?: [],
                'year' => !empty($result) ? $result[0]['year'] : null,
                'count' => count($result ?: [])
            ];

        } catch (\Exception $e) {
            Log::error('获取本年度拟录取名单列表异常: ' . $e->getMessage());
            return ['list' => [], 'year' => null, 'count' => 0];
        }
    }

    function student(){
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }
}
