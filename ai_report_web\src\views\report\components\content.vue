<template>
  <div class="content-container" v-show="isVisible">
    <div class="step-section" v-show="reportData?.school_list.length > 0">
      <div class="step-header">第三部分：院校总览</div>
      <div class="step-content">
        <div class="school-table-container">
          <!-- 表格头部 -->
          <div class="school-table-header">
            <div class="header-cell">序号</div>
            <div class="header-cell">院校名称</div>
            <div class="header-cell">所在地区</div>
            <div class="header-cell">所在省市</div>
            <div class="header-cell">学院</div>
            <div class="header-cell">专业</div>
            <div class="header-cell">专业代码</div>
            <div class="header-cell">最低分</div>
            <div class="header-cell">最低分分差</div>
          </div>

          <!-- 表格内容 -->
          <div class="school-table-body">
            <div
              class="table-row"
              v-for="(item, index) in reportData?.school_list || []"
              :key="item.id"
            >
              <div class="body-cell">{{ index + 1 }}</div>
              <div class="body-cell school-name">
                <div>{{ item.school_name }}</div>
                <div class="school-tags">
                  <span class="tag tag-985" v-if="item.tag_985">985</span>
                  <span class="tag tag-211" v-if="item.tag_211">211</span>
                  <span class="tag tag-double" v-if="item.tag_double"
                    >双一流</span
                  >
                </div>
              </div>
              <div class="body-cell">{{ item.region }}</div>
              <div class="body-cell">{{ item.city }}</div>
              <div class="body-cell">{{ item.college }}</div>
              <div class="body-cell">{{ item.major_name }}</div>
              <div class="body-cell">{{ item.major_code }}</div>
              <div class="body-cell">{{ item.min_score }}</div>
              <div class="body-cell">{{ item.score_diff }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="step-section">
        <div class="step-header">第四部分：院校分析</div>

        <div
          class="step-content"
          v-for="(school, index) in reportData?.recommend_list || []"
          :key="school.school_id"
        >
          <div class="step-num-tag">
            <span>{{ index + 1 }}</span>
            <div class="tag-text">院校推荐</div>
          </div>

          <!-- 院校详情卡片 -->
          <div class="school-detail-card">
            <div class="school-header">
              <div class="school-logo" v-show="school?.school_info?.logo">
                <img
                  :src="
                    'https://' + school?.school_info?.logo ||
                    '@/assets/images/school.png'
                  "
                  alt="学校logo"
                />
              </div>
              <div class="school-info">
                <div class="school-title">
                  <h2>{{ school?.school_name || "" }}</h2>
                  <span class="school-location">{{
                    school?.school_info?.address || ""
                  }}</span>
                </div>
                <div class="school-tags-row">
                  <span
                    class="tag tag-double"
                    v-if="school?.school_info?.is_dual_class"
                    >双一流</span
                  >
                  <span class="tag tag-985" v-if="school?.school_info?.tag_985"
                    >985</span
                  >
                  <span class="tag tag-211" v-if="school?.school_info?.tag_211"
                    >211</span
                  >
                </div>
              </div>
            </div>
            <template v-if="Object.keys(school?.basic_info!).length != 0">
              <h3 class="section-title">院校情况</h3>
              <div class="school-detail-section">
                <!-- C. 总成绩计算公式 -->
                <div
                  class="detail-item"
                  v-if="school?.basic_info?.score_formula"
                >
                  <div class="item-icon">
                    <img
                      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                      alt="标签图标"
                    />
                  </div>
                  <div class="item-content">
                    <div class="content-header">
                      <h4>总成绩计算公式</h4>
                      <div
                        class="action-buttons"
                        v-if="
                          !editingScoreFormula[school.school_id] &&
                          reportGenerationComplete
                        "
                      >
                        <el-button
                          type="text"
                          size="small"
                          @click="startEditScoreFormula(school.school_id)"
                          class="edit-btn"
                        >
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div v-if="!editingScoreFormula[school.school_id]">
                      <p>{{ school?.basic_info?.score_formula }}</p>
                    </div>
                    <div v-else class="edit-container">
                      <el-input
                        v-model="editScoreFormulaText[school.school_id]"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入总成绩计算公式"
                        class="edit-textarea"
                      />
                      <div class="edit-actions">
                        <el-button
                          size="small"
                          @click="saveScoreFormula(school.school_id)"
                          class="save-btn"
                          >保存</el-button
                        >
                        <el-button
                          size="small"
                          @click="cancelEditScoreFormula(school.school_id)"
                          >取消</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <!-- D. 学制与学费 -->
                <div class="detail-item" v-if="school?.basic_info?.study_years">
                  <div class="item-icon">
                    <img
                      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                      alt="标签图标"
                    />
                  </div>
                  <div class="item-content">
                    <div class="content-header">
                      <h4>学制</h4>
                      <div
                        class="action-buttons"
                        v-if="
                          !editingStudyYears[school.school_id] &&
                          reportGenerationComplete
                        "
                      >
                        <el-button
                          type="text"
                          size="small"
                          @click="startEditStudyYears(school.school_id)"
                          class="edit-btn"
                        >
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div v-if="!editingStudyYears[school.school_id]">
                      <p>{{ school?.basic_info?.study_years }}</p>
                    </div>
                    <div v-else class="edit-container">
                      <el-input
                        v-model="editStudyYearsText[school.school_id]"
                        type="textarea"
                        :rows="2"
                        placeholder="请输入学制"
                        class="edit-textarea"
                      />
                      <div class="edit-actions">
                        <el-button
                          size="small"
                          @click="saveStudyYears(school.school_id)"
                          class="save-btn"
                          >保存</el-button
                        >
                        <el-button
                          size="small"
                          @click="cancelEditStudyYears(school.school_id)"
                          >取消</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <div class="detail-item" v-if="school?.basic_info?.tuition_fee">
                  <div class="item-icon">
                    <img
                      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                      alt="标签图标"
                    />
                  </div>
                  <div class="item-content">
                    <div class="content-header">
                      <h4>学费</h4>
                      <div
                        class="action-buttons"
                        v-if="
                          !editingTuitionFee[school.school_id] &&
                          reportGenerationComplete
                        "
                      >
                        <el-button
                          type="text"
                          size="small"
                          @click="startEditTuitionFee(school.school_id)"
                          class="edit-btn"
                        >
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div v-if="!editingTuitionFee[school.school_id]">
                      <p>{{ school?.basic_info?.tuition_fee }}</p>
                    </div>
                    <div v-else class="edit-container">
                      <el-input
                        v-model="editTuitionFeeText[school.school_id]"
                        type="textarea"
                        :rows="2"
                        placeholder="请输入学费"
                        class="edit-textarea"
                      />
                      <div class="edit-actions">
                        <el-button
                          size="small"
                          @click="saveTuitionFee(school.school_id)"
                          class="save-btn"
                          >保存</el-button
                        >
                        <el-button
                          size="small"
                          @click="cancelEditTuitionFee(school.school_id)"
                          >取消</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <h3 class="section-title">初试模块</h3>
              <div class="school-detail-section">
                <!-- E. 初试考试科目 -->
                <div class="detail-item" v-if="school?.basic_info?.exam_range">
                  <div class="item-icon">
                    <img
                      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                      alt="标签图标"
                    />
                  </div>
                  <div class="item-content">
                    <div class="content-header">
                      <h4>初试考试科目</h4>
                      <div
                        class="action-buttons"
                        v-if="
                          !editingExamSubjects[school.school_id] &&
                          reportGenerationComplete
                        "
                      >
                        <el-button
                          type="text"
                          size="small"
                          @click="startEditExamSubjects(school.school_id)"
                          class="edit-btn"
                        >
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div v-if="!editingExamSubjects[school.school_id]">
                      <p>{{ school?.basic_info?.exam_range }}</p>
                    </div>
                    <div v-else class="edit-container">
                      <el-input
                        v-model="editExamSubjectsText[school.school_id]"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入初试考试科目"
                        class="edit-textarea"
                      />
                      <div class="edit-actions">
                        <el-button
                          size="small"
                          @click="saveExamSubjects(school.school_id)"
                          class="save-btn"
                          >保存</el-button
                        >
                        <el-button
                          size="small"
                          @click="cancelEditExamSubjects(school.school_id)"
                          >取消</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 专业课参考书目 -->
                <div
                  class="detail-item"
                  v-if="school?.basic_info?.reference_books"
                >
                  <div class="item-icon">
                    <img
                      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                      alt="标签图标"
                    />
                  </div>
                  <div class="item-content">
                    <div class="content-header">
                      <h4>考试专业课参考书</h4>
                      <div
                        class="action-buttons"
                        v-if="
                          !editingReferenceBooks[school.school_id] &&
                          reportGenerationComplete
                        "
                      >
                        <el-button
                          type="text"
                          size="small"
                          @click="startEditReferenceBooks(school.school_id)"
                          class="edit-btn"
                        >
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div v-if="!editingReferenceBooks[school.school_id]">
                      <p>{{ school?.basic_info?.reference_books }}</p>
                    </div>
                    <div v-else class="edit-container">
                      <el-input
                        v-model="editReferenceBooksText[school.school_id]"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入考试专业课参考书"
                        class="edit-textarea"
                      />
                      <div class="edit-actions">
                        <el-button
                          size="small"
                          @click="saveReferenceBooks(school.school_id)"
                          class="save-btn"
                          >保存</el-button
                        >
                        <el-button
                          size="small"
                          @click="cancelEditReferenceBooks(school.school_id)"
                          >取消</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 复试考核内容 -->
                <div
                  class="detail-item"
                  v-if="school?.basic_info?.retest_score_requirement"
                >
                  <div class="item-icon">
                    <img
                      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                      alt="标签图标"
                    />
                  </div>
                  <div class="item-content">
                    <div class="content-header">
                      <h4>复试考核内容</h4>
                      <div
                        class="action-buttons"
                        v-if="
                          !editingRetestScoreRequirement[school.school_id] &&
                          reportGenerationComplete
                        "
                      >
                        <el-button
                          type="text"
                          size="small"
                          @click="
                            startEditRetestScoreRequirement(school.school_id)
                          "
                          class="edit-btn"
                        >
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div
                      v-if="!editingRetestScoreRequirement[school.school_id]"
                    >
                      <p>{{ school?.basic_info?.retest_score_requirement }}</p>
                    </div>
                    <div v-else class="edit-container">
                      <el-input
                        v-model="
                          editRetestScoreRequirementText[school.school_id]
                        "
                        type="textarea"
                        :rows="3"
                        placeholder="请输入复试考核内容"
                        class="edit-textarea"
                      />
                      <div class="edit-actions">
                        <el-button
                          size="small"
                          @click="saveRetestScoreRequirement(school.school_id)"
                          class="save-btn"
                          >保存</el-button
                        >
                        <el-button
                          size="small"
                          @click="
                            cancelEditRetestScoreRequirement(school.school_id)
                          "
                          >取消</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <!-- 第六步：招生情况 -->

            <div class="step-content-zs">
              <!-- 招生情况标题 -->
              <div
                class="admission-title"
                v-if="
                  school?.admission_data && school.admission_data.length > 0
                "
              >
                招生情况（{{ school.admission_data[0]?.year }}年）
              </div>

              <!-- 招生计划表格 -->
              <table
                class="admission-table"
                v-if="
                  school?.admission_data && school.admission_data.length > 0
                "
              >
                <thead>
                  <tr>
                    <th>招生计划</th>
                    <th>一志愿人数</th>
                    <th>总录取数</th>
                    <th>一志愿录取比</th>
                    <th>调剂人数</th>
                    <th>最高分</th>
                    <th>最低分</th>
                    <th>平均分</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(item, index) in school.admission_data"
                    :key="index"
                  >
                    <td>{{ item.planCount }}</td>
                    <td>{{ item.examCount }}</td>
                    <td>{{ item.admitCount }}</td>
                    <td>{{ item.ratio }}</td>
                    <td>{{ item.studentCount }}</td>
                    <td>{{ item.highestScore }}</td>
                    <td>{{ item.lowestScore }}</td>
                    <td>{{ item.averageScore }}</td>
                  </tr>
                </tbody>
              </table>

              <template v-if="school?.current_year_retest_list.list.length > 0">
                <!-- 一志愿考试名单标题 -->
                <div class="admission-title">一志愿考试名单</div>

                <!-- 一志愿考试名单表格 -->
                <table class="admission-table">
                  <thead>
                    <tr>
                      <th>编号</th>
                      <th>学生姓名</th>
                      <th>政治</th>
                      <th>英语</th>
                      <th>专业课一</th>
                      <th>专业课二</th>
                      <th>初试成绩</th>
                      <th>是否一志愿</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(item, index) in school?.current_year_retest_list
                        ?.list || []"
                      :key="index"
                    >
                      <td>{{ index + 1 }}</td>
                      <td>{{ item.name + "**" }}</td>
                      <td>{{ item.politics_score }}</td>
                      <td>{{ item.english_score }}</td>
                      <td>{{ item.major1_score }}</td>
                      <td>{{ item.major2_score }}</td>
                      <td>{{ item.initial_score }}</td>
                      <td>{{ item.admission_status }}</td>
                    </tr>
                  </tbody>
                </table>
              </template>
              <template v-if="school?.basic_info?.retest_content">
                <!-- 复试模块标题 -->
                <div class="admission-title">复试模块</div>

                <!-- 复试模块内容 -->
                <div class="reexam-container">
                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                      <div class="reexam-title">复试考试内容</div>
                      <div
                        class="action-buttons"
                        v-if="
                          !editingRetestContent[school.school_id] &&
                          reportGenerationComplete
                        "
                      >
                        <el-button
                          type="text"
                          size="small"
                          @click="startEditRetestContent(school.school_id)"
                          class="edit-btn"
                        >
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div class="reexam-content">
                      <div v-if="!editingRetestContent[school.school_id]">
                        {{ school?.basic_info?.retest_content }}
                      </div>
                      <div v-else class="edit-container">
                        <el-input
                          v-model="editRetestContentText[school.school_id]"
                          type="textarea"
                          :rows="4"
                          placeholder="请输入复试考试内容"
                          class="edit-textarea"
                        />
                        <div class="edit-actions">
                          <el-button
                            size="small"
                            @click="saveRetestContent(school.school_id)"
                            class="save-btn"
                            >保存</el-button
                          >
                          <el-button
                            size="small"
                            @click="cancelEditRetestContent(school.school_id)"
                            >取消</el-button
                          >
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- <div class="reexam-card">
                    <div class="reexam-header">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                      <div class="reexam-title">录取要求</div>
                      <el-button
                        v-if="
                          !editingAdmissionRequirements[school.school_id] &&
                          reportGenerationComplete
                        "
                        type="text"
                        size="small"
                        @click="
                          startEditAdmissionRequirements(school.school_id)
                        "
                        class="edit-btn"
                      >
                        编辑
                      </el-button>
                    </div>
                    <div class="reexam-content">
                      <div
                        v-if="!editingAdmissionRequirements[school.school_id]"
                      >
                        {{ school?.basic_info?.admission_requirements }}
                      </div>
                      <div v-else class="edit-container">
                        <el-input
                          v-model="
                            editAdmissionRequirementsText[school.school_id]
                          "
                          type="textarea"
                          :rows="4"
                          placeholder="请输入录取要求"
                          class="edit-textarea"
                        />
                        <div class="edit-actions">
                          <el-button
                            size="small"
                            @click="saveAdmissionRequirements(school.school_id)"
                            class="save-btn"
                            >保存</el-button
                          >
                          <el-button
                            size="small"
                            @click="
                              cancelEditAdmissionRequirements(school.school_id)
                            "
                            >取消</el-button
                          >
                        </div>
                      </div>
                    </div>
                  </div> -->
                </div>
              </template>

              <template
                v-if="school?.current_year_admission_list?.list.length > 0"
              >
                <!-- 报录取名单标题 -->
                <div class="admission-title">拟录取名单</div>

                <!-- 报录取名单表格 -->
                <table class="admission-table">
                  <thead>
                    <tr>
                      <th>编号</th>
                      <th>学生姓名</th>
                      <th>初试成绩</th>
                      <th>复试成绩</th>
                      <th>两项总成绩</th>
                      <th>一志愿学校</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(item, index) in school
                        ?.current_year_admission_list?.list || []"
                      :key="index"
                    >
                      <td>{{ index + 1 }}</td>
                      <td>{{ item.name + "**" }}</td>
                      <td>{{ item.initial_score }}</td>
                      <td>{{ item.retest_score }}</td>
                      <td>{{ item.total_score }}</td>
                      <td>{{ item.first_choice_school }}</td>
                    </tr>
                  </tbody>
                </table>
              </template>
              <!-- 综合建议标题 -->
              <div class="admission-title">综合建议</div>

              <!-- 综合建议内容 -->
              <div class="reexam-container">
                <div class="reexam-card">
                  <div class="reexam-header">
                    <img
                      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                      alt="标签图标"
                    />
                    <div class="reexam-title">竞争难度分析</div>
                    <div
                      class="action-buttons"
                      v-if="
                        !editingDifficulty[school.school_id] &&
                        reportGenerationComplete
                      "
                    >
                      <el-button
                        type="text"
                        size="small"
                        @click="startEditDifficulty(school.school_id)"
                        class="edit-btn"
                      >
                        编辑
                      </el-button>
                      <el-button
                        type="text"
                        size="small"
                        @click="
                          regenerateField(
                            school.school_id,
                            'competition_difficulty'
                          )
                        "
                        class="regenerate-btn"
                        :loading="
                          regeneratingFields[
                            `${school.school_id}_competition_difficulty`
                          ]
                        "
                        :disabled="
                          regeneratingFields[
                            `${school.school_id}_competition_difficulty`
                          ]
                        "
                      >
                        {{
                          regeneratingFields[
                            `${school.school_id}_competition_difficulty`
                          ]
                            ? "生成中..."
                            : "重新生成"
                        }}
                      </el-button>
                    </div>
                  </div>
                  <div class="reexam-content">
                    <div v-if="!editingDifficulty[school.school_id]">
                      {{ school.difficulty_analysis }}
                    </div>
                    <div v-else class="edit-container">
                      <el-input
                        v-model="editDifficultyText[school.school_id]"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入竞争难度分析"
                        class="edit-textarea"
                      />
                      <div class="edit-actions">
                        <el-button
                          size="small"
                          @click="saveDifficulty(school.school_id)"
                          >保存</el-button
                        >
                        <el-button
                          size="small"
                          @click="cancelEditDifficulty(school.school_id)"
                          >取消</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <div class="reexam-card">
                  <div class="reexam-header">
                    <img
                      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                      alt="标签图标"
                    />
                    <div class="reexam-title">备考目标建议</div>
                    <div
                      class="action-buttons"
                      v-if="
                        !editingSuggestion[school.school_id] &&
                        reportGenerationComplete
                      "
                    >
                      <el-button
                        type="text"
                        size="small"
                        @click="startEditSuggestion(school.school_id)"
                        class="edit-btn"
                      >
                        编辑
                      </el-button>
                      <el-button
                        type="text"
                        size="small"
                        @click="
                          regenerateField(school.school_id, 'suggestions')
                        "
                        class="regenerate-btn"
                        :loading="
                          regeneratingFields[`${school.school_id}_suggestions`]
                        "
                        :disabled="
                          regeneratingFields[`${school.school_id}_suggestions`]
                        "
                      >
                        {{
                          regeneratingFields[`${school.school_id}_suggestions`]
                            ? "生成中..."
                            : "重新生成"
                        }}
                      </el-button>
                    </div>
                  </div>
                  <div class="reexam-content">
                    <div v-if="!editingSuggestion[school.school_id]">
                      {{ school.suggest }}
                    </div>
                    <div v-else class="edit-container">
                      <el-input
                        v-model="editSuggestionText[school.school_id]"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入备考目标建议"
                        class="edit-textarea"
                      />
                      <div class="edit-actions">
                        <el-button
                          size="small"
                          @click="saveSuggestion(school.school_id)"
                          >保存</el-button
                        >
                        <el-button
                          size="small"
                          @click="cancelEditSuggestion(school.school_id)"
                          >取消</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="recommend-school-wraper">
      <!-- 推荐综合性价比高的院校标题 -->
      <div class="step-num-tag">
        <span>11</span>
        <div class="tag-text">推荐综合性价比高的院校</div>
      </div>

      <!-- 推荐综合性价比高的院校内容 -->
      <div class="recommend-school-container">
        <div class="recommend-school-card">
          <div class="recommend-school-header">
            <div class="recommend-icon">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                alt="标签图标"
              />
            </div>
            <div class="recommend-title">推荐原因</div>
            <div
              class="action-buttons"
              v-if="
                !editingHighRecommendReason['high_recommend'] &&
                reportGenerationComplete
              "
            >
              <el-button
                type="text"
                size="small"
                @click="startEditHighRecommendReason('high_recommend')"
                class="edit-btn"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="regenerate-btn"
                @click="regenerateField('-1', 'highrecommendreason')"
                :loading="regeneratingFields['-1_highrecommendreason']"
                :disabled="regeneratingFields['-1_highrecommendreason']"
              >
                {{ regeneratingFields['-1_highrecommendreason'] ? '生成中...' : '重新生成' }}
              </el-button>
            </div>
          </div>
          <div class="recommend-school-content">
            <div v-if="!editingHighRecommendReason['high_recommend']">
              {{
                reportData?.high_recommend_list?.[0]?.reason || "暂无推荐信息"
              }}
            </div>
            <div v-else class="edit-container">
              <el-input
                v-model="editHighRecommendReasonText['high_recommend']"
                type="textarea"
                :rows="4"
                placeholder="请输入高性价比院校推荐原因"
                class="edit-textarea"
              />
              <div class="edit-actions">
                <el-button
                  size="small"
                  @click="saveHighRecommendReason('high_recommend')"
                  class="save-btn"
                  >保存</el-button
                >
                <el-button
                  size="small"
                  @click="cancelEditHighRecommendReason('high_recommend')"
                  >取消</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  defineExpose,
  nextTick,
  watch,
  type Ref,
  onUnmounted,
} from "vue";
import {
  ai_recommendation,
  saveReport as saveReportApi,
  streamAiRecommendation,
  getSchoolDetail,
  saveReportData,
  saveReportFieldEdit,
} from "@/api/report";
import { ElMessage, ElNotification } from "element-plus";
import { useUserReportStore } from "@/store/modules/report";
import { useUserStore } from "@/store/modules/user";
const userstore = useUserStore();
const reportDataStore = useUserReportStore();
import type {
  AiRecommendationParams,
  AiRecommendationResult,
  RecommendSchool,
  HighRecommendSchool,
} from "@/types";
import school from "@/api/school";

// 本地特有的数据类型定义（index.ts 中没有的）

interface NationalLineData {
  subject_code: string;
  subject_name: string;
  years: string[];
  a_total: number[];
  a_single_100: number[];
  a_single_over100: number[];
  b_total: number[];
  b_single_100: number[];
  b_single_over100: number[];
}

interface ReportData {
  targetMajorName?: string;
  majorCode?: string;
  firstLevelSubject?: string;
}

// 扩展 window 对象类型
declare global {
  interface Window {
    reportData?: ReportData;
  }
}

// 国家线数据
const nationalLineData: Ref<NationalLineData> = ref({
  subject_code: "",
  subject_name: "",
  years: [],
  a_total: [],
  a_single_100: [],
  a_single_over100: [],
  b_total: [],
  b_single_100: [],
  b_single_over100: [],
});

// 更新国家线数据
const updateNationalLineData = (data: NationalLineData): void => {
  console.log("更新国家线数据:", data);
  nationalLineData.value = data;
  // 国家线数据现在由MajorAnalysis组件处理，这里不再需要初始化图表
};

// 控制组件显示/隐藏的状态
const isVisible: Ref<boolean> = ref(false);

// 监听 isVisible 变化
watch(isVisible, (newVal: boolean) => {
  if (newVal) {
    // 当组件变为可见时，确保图表正确初始化和渲染
    nextTick(() => {
      // 在组件显示后，需要重新初始化图表
      // 这里不需要重新初始化图表，因为图表初始化已经移到了MajorAnalysis组件中
    });
  }
});

// 显示内容
const show = (): void => {
  console.log("显示content组件");
  isVisible.value = true;
  // 在组件显示后，需要重新初始化图表
  nextTick(() => {
    console.log("nextTick后初始化图表");
    // 这里不需要重新初始化图表，因为图表初始化已经移到了MajorAnalysis组件中
  });
};

// 隐藏内容
const hide = (): void => {
  isVisible.value = false;
};

// 切换显示/隐藏状态
const toggle = (): void => {
  isVisible.value = !isVisible.value;
  // 如果切换为显示状态，需要重新初始化图表
  if (isVisible.value) {
    nextTick(() => {
      // 这里不需要重新初始化图表，因为图表初始化已经移到了MajorAnalysis组件中
    });
  }
};
let reportData: Ref<AiRecommendationResult> = ref<AiRecommendationResult>({
  recommend_list: [],
  high_recommend_list: [],
  school_list: [],
} as AiRecommendationResult);

// 编辑相关状态
const editingDifficulty = ref<Record<string, boolean>>({});
const editingSuggestion = ref<Record<string, boolean>>({});
const editDifficultyText = ref<Record<string, string>>({});
const editSuggestionText = ref<Record<string, string>>({});

// 新增字段的编辑状态
const editingScoreFormula = ref<Record<string, boolean>>({});
const editingStudyYears = ref<Record<string, boolean>>({});
const editingTuitionFee = ref<Record<string, boolean>>({});
const editingExamSubjects = ref<Record<string, boolean>>({});
const editingReferenceBooks = ref<Record<string, boolean>>({});
const editingRetestContent = ref<Record<string, boolean>>({});
const editingRetestScoreRequirement = ref<Record<string, boolean>>({});
const editingHighRecommendReason = ref<Record<string, boolean>>({});
const editingAdmissionRequirements = ref<Record<string, boolean>>({});

// 重新生成字段的加载状态
const regeneratingFields = ref<Record<string, boolean>>({});

// 新增字段的编辑文本
const editScoreFormulaText = ref<Record<string, string>>({});
const editStudyYearsText = ref<Record<string, string>>({});
const editTuitionFeeText = ref<Record<string, string>>({});
const editExamSubjectsText = ref<Record<string, string>>({});
const editReferenceBooksText = ref<Record<string, string>>({});
const editRetestContentText = ref<Record<string, string>>({});
const editRetestScoreRequirementText = ref<Record<string, string>>({});
const editHighRecommendReasonText = ref<Record<string, string>>({});
const editAdmissionRequirementsText = ref<Record<string, string>>({});

const saving = ref(false);
const currentReportId = ref<string | number>("");
const pdfUrl = ref<string>("");
const reportGenerationComplete = ref(false); // 报告是否完全生成完毕

// 保存单个字段编辑的函数
const saveFieldEdit = async (
  schoolId: string,
  fieldName: string,
  fieldValue: string
) => {
  if (!currentReportId.value) {
    throw new Error("报告ID不存在");
  }

  // 检查是否是高性价比院校
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  const isHighRecommend = school?.is_high_recommend || 0;

  await saveReportFieldEdit({
    report_id: currentReportId.value,
    school_id: parseInt(schoolId),
    field_name: fieldName,
    field_value: fieldValue,
    is_high_recommend: isHighRecommend,
  });
};

const regenerateField = (schoolId: string, fieldName: string) => {
  if (!currentReportId.value) {
    ElMessage.error("报告ID不能为空");
    return;
  }

  // 设置加载状态
  const loadingKey = `${schoolId}_${fieldName}`;
  regeneratingFields.value[loadingKey] = true;

  // 构建SSE URL
  const baseUrl = import.meta.env.VITE_API_BASE_URL || "http://localhost:8787";
  const url = `${baseUrl}/remote/regenerate_field?report_id=${currentReportId.value}&school_id=${schoolId}&field_name=${fieldName}`;

  // 创建EventSource连接
  const eventSource = new EventSource(url);

  let accumulatedContent = "";

  eventSource.onmessage = (event) => {
    try {
      const data = event.data;

      if (data && data !== "重新生成完成") {
        accumulatedContent += data;

        // 实时更新UI
        updateFieldContent(schoolId, fieldName, accumulatedContent);
      }
    } catch (error) {
      console.error("解析SSE数据失败:", error);
    }
  };

  eventSource.addEventListener("complete", (event) => {
    console.log("重新生成完成:", event.data);
    regeneratingFields.value[loadingKey] = false;
    eventSource.close();
    ElMessage.success("重新生成完成");
  });

  eventSource.addEventListener("error", (event) => {
    console.error("SSE连接错误:", event);
    regeneratingFields.value[loadingKey] = false;
    eventSource.close();
    ElMessage.error("重新生成失败，请重试");
  });

  eventSource.onerror = (error) => {
    console.error("EventSource连接失败:", error);
    regeneratingFields.value[loadingKey] = false;
    eventSource.close();
    ElMessage.error("连接失败，请检查网络");
  };
};

// 更新字段内容的函数
const updateFieldContent = (
  schoolId: string,
  fieldName: string,
  content: string
) => {
  if (!reportData.value) return;

  if (schoolId === "-1") {
    // 高性价比院校推荐原因
    if (
      reportData.value.high_recommend_list &&
      reportData.value.high_recommend_list.length > 0
    ) {
      reportData.value.high_recommend_list[0].reason = content;
    }
  } else {
    // 普通院校字段
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id.toString() === schoolId
    );
    if (school) {
      if (fieldName === "competition_difficulty") {
        school.difficulty_analysis = content;
      } else if (fieldName === "suggestions") {
        school.suggest = content;
      }
    }
  }
};

let schoolInfoIndex = 0;

// 更新setReportId方法
const setReportId = async (report_id: string | number): Promise<void> => {
  currentReportId.value = report_id;
  schoolInfoIndex = 0;

  let schoolItem: RecommendSchool = {
    school_id: "",
    school_name: "",
    major_name: "",
    difficulty_analysis: "",
    suggest: "",
    reason: "",
    admission_stats: {
      yearly_stats: [],
      has_data: false,
    },
    retest_stats: {},
    basic_info: {},
    school_info: {},
    current_year_retest_list: {
      list: [],
      year: null,
      count: 0,
    },
    current_year_admission_list: {
      list: [],
      year: null,
      count: 0,
    },
  };
  reportData.value.recommend_list.push(schoolItem);

  try {
    let recievedata = "";
    const params: AiRecommendationParams = { report_id };
    console.log("开始请求SSE数据，报告ID:", report_id);
    let getData = false;
    // SSE流式接口调用
    streamAiRecommendation(params, (data) => {
      console.log("type", data.type);
      if (data.type == "message") {
        // 数据获取完成标记
        if (!getData) {
          getData = true;
        }

        recievedata = recievedata + data.data;
        console.log("recievedata:", recievedata);
        // 处理新的 A-K 格式数据
        if (!recievedata.includes("L.") && !recievedata.includes("M.")) {
          const currentSchool =
            reportData.value.recommend_list[schoolInfoIndex];

          // A. 学校名称
          if (recievedata.includes("A.") && !recievedata.includes("B.")) {
            currentSchool.school_name =
              recievedata.split("A.")[1]?.trim() || "";
          }
          // B. 专业名称
          else if (recievedata.includes("B.") && !recievedata.includes("C.")) {
            currentSchool.major_name = recievedata.split("B.")[1]?.trim() || "";
          }
          // C. 总成绩计算公式
          else if (recievedata.includes("C.") && !recievedata.includes("D.")) {
            if (!currentSchool.basic_info) currentSchool.basic_info = {};
            currentSchool.basic_info.score_formula =
              recievedata.split("C.")[1]?.trim() || "";
          }
          // D. 学制
          else if (recievedata.includes("D.") && !recievedata.includes("N.")) {
            if (!currentSchool.basic_info) currentSchool.basic_info = {};
            currentSchool.basic_info.study_years =
              recievedata.split("D.")[1]?.trim() || "";
          }
          //N. 学费
          else if (recievedata.includes("N.") && !recievedata.includes("E.")) {
            if (!currentSchool.basic_info) currentSchool.basic_info = {};
            currentSchool.basic_info.tuition_fee =
              recievedata.split("N.")[1]?.trim() || "";
          }
          // E. 初试考试科目
          else if (recievedata.includes("E.") && !recievedata.includes("F.")) {
            if (!currentSchool.basic_info) currentSchool.basic_info = {};
            currentSchool.basic_info.exam_range =
              recievedata.split("E.")[1]?.trim() || "";
          }
          // F. 专业课参考书目
          else if (recievedata.includes("F.") && !recievedata.includes("G.")) {
            if (!currentSchool.basic_info) currentSchool.basic_info = {};
            currentSchool.basic_info.reference_books =
              recievedata.split("F.")[1]?.trim() || "";
          }
          // G. 复试分数线基本要求
          else if (recievedata.includes("G.") && !recievedata.includes("H.")) {
            if (!currentSchool.basic_info) currentSchool.basic_info = {};
            currentSchool.basic_info.retest_score_requirement =
              recievedata.split("G.")[1]?.trim() || "";
          }
          // H. 复试考试内容
          else if (recievedata.includes("H.") && !recievedata.includes("J.")) {
            if (!currentSchool.basic_info) currentSchool.basic_info = {};
            currentSchool.basic_info.retest_content =
              recievedata.split("H.")[1]?.trim() || "";
          }
          // I. 录取要求
          // else if (recievedata.includes("I.") && !recievedata.includes("J.")) {
          //   if (!currentSchool.basic_info) currentSchool.basic_info = {};
          //   currentSchool.basic_info.admission_requirements =
          //     recievedata.split("I.")[1]?.trim() || "";
          // }
          // J. 竞争难度分析
          else if (recievedata.includes("J.") && !recievedata.includes("K.")) {
            currentSchool.difficulty_analysis =
              recievedata.split("J.")[1]?.trim() || "";
          }
          // K. 备考目标建议
          else if (recievedata.includes("K.") && !recievedata.includes("L.")) {
            currentSchool.suggest = recievedata.split("K.")[1]?.trim() || "";
          }
        } else if (!recievedata.includes("M.") && recievedata.includes("L.")) {
          // L. 院校信息结束标记
          const recievedataArr = recievedata.split("L.");
          const afterLContent = recievedataArr[recievedataArr.length - 1];
          recievedata = afterLContent;

          // 调用API获取当前学校的详细信息
          const currentSchool =
            reportData.value.recommend_list[schoolInfoIndex];
          if (
            currentSchool &&
            currentSchool.school_name &&
            currentReportId.value
          ) {
            getSchoolDetail({
              school_name: currentSchool.school_name,
              report_id: currentReportId.value,
            })
              .then((schoolDetailResponse) => {
                if (
                  schoolDetailResponse.code === 0 &&
                  schoolDetailResponse.data
                ) {
                  // 将获取到的详细信息填充到当前学校对象中，但保留原有的basic_info
                  const { basic_info: originalBasicInfo, school_name } =
                    currentSchool;

                  Object.assign(currentSchool, schoolDetailResponse.data.info);
                  // 恢复原有的basic_info，避免被覆盖
                  if (originalBasicInfo) {
                    currentSchool.basic_info = originalBasicInfo;
                  }
                  if (school_name) {
                    currentSchool.school_name = school_name;
                  }
                  // 添加招生情况数据
                  if (schoolDetailResponse.data.admission_data) {
                    currentSchool.admission_data =
                      schoolDetailResponse.data.admission_data;
                  }
                  reportData.value.school_list.push(
                    schoolDetailResponse.data.item
                  );
                  console.log(
                    "成功获取学校详细信息:",
                    currentSchool.school_name,
                    schoolDetailResponse.data
                  );
                } else {
                  console.warn(
                    "获取学校详细信息失败:",
                    currentSchool.school_name,
                    schoolDetailResponse.msg
                  );
                }
              })
              .catch((error) => {
                console.error("调用学校详细信息API失败:", error);
              });
          }

          // 智能判断是否需要创建下一个院校
          // 更宽松的判断逻辑：如果L.后面没有包含M.标记，就认为可能还有更多院校
          const hasM = afterLContent.includes("M.");
          const hasA = afterLContent.includes("A.");
          const shouldCreateNextSchool = !hasM; // 只要没有M标记，就可能还有院校

          console.log("L.标记处理 - afterLContent:", afterLContent);
          console.log(
            "L.标记处理 - hasM:",
            hasM,
            "hasA:",
            hasA,
            "shouldCreateNextSchool:",
            shouldCreateNextSchool
          );
          console.log(
            "L.标记处理 - 当前院校总数:",
            reportData.value.recommend_list.length
          );

          if (shouldCreateNextSchool) {
            // 准备下一个院校的数据结构
            let schoolItem: RecommendSchool = {
              school_id: "",
              school_name: "",
              major_name: "",
              difficulty_analysis: "",
              suggest: "",
              reason: "",
              admission_stats: {
                yearly_stats: [],
                has_data: false,
              },
              retest_stats: {},
              basic_info: {},
              school_info: {},
              current_year_retest_list: {
                list: [],
                year: null,
                count: 0,
              },
              current_year_admission_list: {
                list: [],
                year: null,
                count: 0,
              },
            };
            reportData.value.recommend_list.push(schoolItem);
          }

          schoolInfoIndex++;
        } else if (recievedata.includes("M.")) {
          // M. 高性价比院校推荐
          console.log(
            "收到M.标记 - 处理前recommend_list数量:",
            reportData.value.recommend_list.length
          );
          console.log("收到M.标记 - 原始数据:", recievedata);

          let high_recommend_school_info =
            recievedata.split("M.")[1]?.trim() || "";

          // 确保high_recommend_list数组已初始化
          if (!reportData.value.high_recommend_list[0]) {
            reportData.value.high_recommend_list = [
              {
                reason: "",
                school_name: "",
                major_name: "",
                school_id: "",
              },
            ];
          }
          reportData.value.high_recommend_list[0].reason =
            high_recommend_school_info;

          // 强制触发Vue响应式更新
          nextTick(() => {
            console.log(
              "M.标记处理 - nextTick后recommend_list数量:",
              reportData.value.recommend_list.length
            );
          });
        }
      } else if (data.type === "chunk") {
        // 更新加载文本以显示实时进度
        //currentLoadingText.value = data.content;
      } else if (data.type === "end") {
        const currentSchool: RecommendSchool =
          reportData.value.recommend_list[schoolInfoIndex];
        if (
          currentSchool &&
          currentSchool.school_name &&
          currentReportId.value
        ) {
          getSchoolDetail({
            school_name: currentSchool.school_name,
            report_id: currentReportId.value,
          })
            .then((schoolDetailResponse) => {
              if (
                schoolDetailResponse.code === 0 &&
                schoolDetailResponse.data
              ) {
                // 将获取到的详细信息填充到当前学校对象中，但保留原有的basic_info
                const { basic_info: originalBasicInfo, school_name } =
                  currentSchool;

                Object.assign(currentSchool, schoolDetailResponse.data.info);
                // 恢复原有的basic_info，避免被覆盖
                if (originalBasicInfo) {
                  currentSchool.basic_info = originalBasicInfo;
                }
                if (school_name) {
                  currentSchool.school_name = school_name;
                }
                // 添加招生情况数据
                if (schoolDetailResponse.data.admission_data) {
                  currentSchool.admission_data =
                    schoolDetailResponse.data.admission_data;
                }
                reportData.value.school_list.push(
                  schoolDetailResponse.data.item
                );
                console.log(
                  "成功获取学校详细信息:",
                  currentSchool.school_name,
                  schoolDetailResponse.data
                );
              } else {
                console.warn(
                  "获取学校详细信息失败:",
                  currentSchool.school_name,
                  schoolDetailResponse.msg
                );
              }

              // 数据接收完毕后，清理空院校并保存报告数据
              cleanupEmptySchoolsAtEnd();
              extractAndSaveReportData();
              //保存完整的报告数据到状态管理
              reportDataStore.setReportData(reportData.value);
            })
            .catch((error) => {
              console.error("调用学校详细信息API失败:", error);
              // 即使获取详细信息失败，也要保存基本数据
              extractAndSaveReportData();
            });
        } else {
          // 没有学校数据时也要清理并保存
          cleanupEmptySchoolsAtEnd();
          extractAndSaveReportData();
        }

        // 设置报告生成完毕状态
        reportGenerationComplete.value = true;

        console.log("end", reportData);
      }
    }).catch((error) => {
      console.error("SSE流式接口错误:", error);
    });
  } catch (error) {
    console.error("加载报告数据失败:", error);
  }
};

// 编辑相关方法
const startEditDifficulty = (schoolId: string) => {
  editingDifficulty.value[schoolId] = true;
  // 找到对应的学校数据
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editDifficultyText.value[schoolId] = school.difficulty_analysis || "";
  }
};

const startEditSuggestion = (schoolId: string) => {
  editingSuggestion.value[schoolId] = true;
  // 找到对应的学校数据
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editSuggestionText.value[schoolId] = school.suggest || "";
  }
};

const saveDifficulty = async (schoolId: string) => {
  try {
    // 更新本地数据
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      school.difficulty_analysis = editDifficultyText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "competition_difficulty",
      editDifficultyText.value[schoolId]
    );

    editingDifficulty.value[schoolId] = false;
    ElMessage.success("竞争难度分析保存成功");
  } catch (error) {
    console.error("保存竞争难度分析失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const saveSuggestion = async (schoolId: string) => {
  try {
    // 更新本地数据
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      school.suggest = editSuggestionText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "suggestions",
      editSuggestionText.value[schoolId]
    );

    editingSuggestion.value[schoolId] = false;
    ElMessage.success("备考建议保存成功");
  } catch (error) {
    console.error("保存备考建议失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

// 保存编辑的内容到数据库
const saveEditedContent = async () => {
  if (!currentReportId.value) {
    throw new Error("报告ID不存在");
  }

  // 准备保存数据 - 包含所有可编辑字段
  const school_list = reportData.value.recommend_list
    .map((school) => {
      // 判断是否是高推荐院校
      const isHighRecommend = reportData.value.high_recommend_list?.some(
        (highSchool) => highSchool.school_name === school.school_name
      )
        ? 1
        : 0;

      return {
        school_id: Number(school.school_id) || 0,
        school_name: school.school_name || "",
        is_high_recommend: isHighRecommend,
        difficulty_analysis: school.difficulty_analysis || "",
        suggest: school.suggest || "",
        reason: school.reason || "",
        score_formula: school.basic_info?.score_formula || "",
        study_years: school.basic_info?.study_years || "",
        tuition_fee: school.basic_info?.tuition_fee || "",
        exam_subjects: school.basic_info?.exam_range || "",
        reference_books: school.basic_info?.reference_books || "",
        retest_content: school.basic_info?.retest_content || "",
        retest_score_requirement:
          school.basic_info?.retest_score_requirement || "",
        high_recommend_reason: isHighRecommend
          ? reportData.value.high_recommend_list?.[0]?.reason || ""
          : "",
        admission_requirements: school.basic_info?.admission_requirements || "",
      };
    })
    .filter((school) => school.school_name); // 过滤掉空的学校名称

  // 调用后端API保存
  const saveData = {
    report_id: currentReportId.value,
    school_list: school_list,
  };

  await saveReportData(saveData);
};

const cancelEditDifficulty = (schoolId: string) => {
  editingDifficulty.value[schoolId] = false;
  delete editDifficultyText.value[schoolId];
};

const cancelEditSuggestion = (schoolId: string) => {
  editingSuggestion.value[schoolId] = false;
  delete editSuggestionText.value[schoolId];
};

// 新增字段的编辑方法
const startEditScoreFormula = (schoolId: string) => {
  editingScoreFormula.value[schoolId] = true;
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editScoreFormulaText.value[schoolId] =
      school.basic_info?.score_formula || "";
  }
};

const saveScoreFormula = async (schoolId: string) => {
  try {
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      if (!school.basic_info) school.basic_info = {};
      school.basic_info.score_formula = editScoreFormulaText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "score_formula",
      editScoreFormulaText.value[schoolId]
    );

    editingScoreFormula.value[schoolId] = false;
    ElMessage.success("总成绩计算公式保存成功");
  } catch (error) {
    console.error("保存总成绩计算公式失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditScoreFormula = (schoolId: string) => {
  editingScoreFormula.value[schoolId] = false;
  delete editScoreFormulaText.value[schoolId];
};

const startEditStudyYears = (schoolId: string) => {
  editingStudyYears.value[schoolId] = true;
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editStudyYearsText.value[schoolId] = school.basic_info?.study_years || "";
  }
};

const saveStudyYears = async (schoolId: string) => {
  try {
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      if (!school.basic_info) school.basic_info = {};
      school.basic_info.study_years = editStudyYearsText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "study_years",
      editStudyYearsText.value[schoolId]
    );

    editingStudyYears.value[schoolId] = false;
    ElMessage.success("学制保存成功");
  } catch (error) {
    console.error("保存学制失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditStudyYears = (schoolId: string) => {
  editingStudyYears.value[schoolId] = false;
  delete editStudyYearsText.value[schoolId];
};

const startEditTuitionFee = (schoolId: string) => {
  editingTuitionFee.value[schoolId] = true;
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editTuitionFeeText.value[schoolId] = school.basic_info?.tuition_fee || "";
  }
};

const saveTuitionFee = async (schoolId: string) => {
  try {
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      if (!school.basic_info) school.basic_info = {};
      school.basic_info.tuition_fee = editTuitionFeeText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "tuition_fee",
      editTuitionFeeText.value[schoolId]
    );

    editingTuitionFee.value[schoolId] = false;
    ElMessage.success("学费保存成功");
  } catch (error) {
    console.error("保存学费失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditTuitionFee = (schoolId: string) => {
  editingTuitionFee.value[schoolId] = false;
  delete editTuitionFeeText.value[schoolId];
};

const startEditExamSubjects = (schoolId: string) => {
  editingExamSubjects.value[schoolId] = true;
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editExamSubjectsText.value[schoolId] = school.basic_info?.exam_range || "";
  }
};

const saveExamSubjects = async (schoolId: string) => {
  try {
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      if (!school.basic_info) school.basic_info = {};
      school.basic_info.exam_range = editExamSubjectsText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "exam_subjects",
      editExamSubjectsText.value[schoolId]
    );

    editingExamSubjects.value[schoolId] = false;
    ElMessage.success("初试考试科目保存成功");
  } catch (error) {
    console.error("保存初试考试科目失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditExamSubjects = (schoolId: string) => {
  editingExamSubjects.value[schoolId] = false;
  delete editExamSubjectsText.value[schoolId];
};

const startEditReferenceBooks = (schoolId: string) => {
  editingReferenceBooks.value[schoolId] = true;
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editReferenceBooksText.value[schoolId] =
      school.basic_info?.reference_books || "";
  }
};

const saveReferenceBooks = async (schoolId: string) => {
  try {
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      if (!school.basic_info) school.basic_info = {};
      school.basic_info.reference_books =
        editReferenceBooksText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "reference_books",
      editReferenceBooksText.value[schoolId]
    );

    editingReferenceBooks.value[schoolId] = false;
    ElMessage.success("考试专业课参考书保存成功");
  } catch (error) {
    console.error("保存考试专业课参考书失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditReferenceBooks = (schoolId: string) => {
  editingReferenceBooks.value[schoolId] = false;
  delete editReferenceBooksText.value[schoolId];
};

const startEditRetestContent = (schoolId: string) => {
  editingRetestContent.value[schoolId] = true;
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editRetestContentText.value[schoolId] =
      school.basic_info?.retest_content || "";
  }
};

const saveRetestContent = async (schoolId: string) => {
  try {
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      if (!school.basic_info) school.basic_info = {};
      school.basic_info.retest_content = editRetestContentText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "retest_content",
      editRetestContentText.value[schoolId]
    );

    editingRetestContent.value[schoolId] = false;
    ElMessage.success("复试考试内容保存成功");
  } catch (error) {
    console.error("保存复试考试内容失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditRetestContent = (schoolId: string) => {
  editingRetestContent.value[schoolId] = false;
  delete editRetestContentText.value[schoolId];
};

const startEditRetestScoreRequirement = (schoolId: string) => {
  editingRetestScoreRequirement.value[schoolId] = true;
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editRetestScoreRequirementText.value[schoolId] =
      school.basic_info?.retest_score_requirement || "";
  }
};

const saveRetestScoreRequirement = async (schoolId: string) => {
  try {
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      if (!school.basic_info) school.basic_info = {};
      school.basic_info.retest_score_requirement =
        editRetestScoreRequirementText.value[schoolId];
    }

    // 调用新的编辑保存API
    await saveFieldEdit(
      schoolId,
      "retest_score_requirement",
      editRetestScoreRequirementText.value[schoolId]
    );

    editingRetestScoreRequirement.value[schoolId] = false;
    ElMessage.success("复试考核内容保存成功");
  } catch (error) {
    console.error("保存复试考核内容失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditRetestScoreRequirement = (schoolId: string) => {
  editingRetestScoreRequirement.value[schoolId] = false;
  delete editRetestScoreRequirementText.value[schoolId];
};

const startEditHighRecommendReason = (key: string) => {
  editingHighRecommendReason.value[key] = true;
  if (reportData.value.high_recommend_list?.[0]?.reason) {
    editHighRecommendReasonText.value[key] =
      reportData.value.high_recommend_list[0].reason;
  }
};

const saveHighRecommendReason = async (key: string) => {
  try {
    if (reportData.value.high_recommend_list?.[0]) {
      reportData.value.high_recommend_list[0].reason =
        editHighRecommendReasonText.value[key];
    }

    // 对于高性价比院校，需要特殊处理
    // 找到高性价比院校对应的school_id
    const highRecommendSchool = reportData.value.high_recommend_list?.[0];
    if (highRecommendSchool && highRecommendSchool.school_id) {
      await saveFieldEdit(
        highRecommendSchool.school_id,
        "high_recommend_reason",
        editHighRecommendReasonText.value[key]
      );
    } else {
      // 如果没有school_id，使用特殊标识
      await saveReportFieldEdit({
        report_id: currentReportId.value,
        school_id: 0, // 高性价比院校使用0作为特殊标识
        field_name: "high_recommend_reason",
        field_value: editHighRecommendReasonText.value[key],
        is_high_recommend: 1,
      });
    }

    editingHighRecommendReason.value[key] = false;
    ElMessage.success("高性价比院校推荐原因保存成功");
  } catch (error) {
    console.error("保存高性价比院校推荐原因失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditHighRecommendReason = (key: string) => {
  editingHighRecommendReason.value[key] = false;
  delete editHighRecommendReasonText.value[key];
};

const startEditAdmissionRequirements = (schoolId: string) => {
  editingAdmissionRequirements.value[schoolId] = true;
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editAdmissionRequirementsText.value[schoolId] =
      school.basic_info?.admission_requirements || "";
  }
};

const saveAdmissionRequirements = async (schoolId: string) => {
  try {
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      if (!school.basic_info) school.basic_info = {};
      school.basic_info.admission_requirements =
        editAdmissionRequirementsText.value[schoolId];
    }
    await saveEditedContent();
    editingAdmissionRequirements.value[schoolId] = false;
    ElMessage.success("录取要求保存成功");
  } catch (error) {
    console.error("保存录取要求失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const cancelEditAdmissionRequirements = (schoolId: string) => {
  editingAdmissionRequirements.value[schoolId] = false;
  delete editAdmissionRequirementsText.value[schoolId];
};

// 在数据流结束时清理空的院校数据
const cleanupEmptySchoolsAtEnd = (): void => {
  console.log("=== 开始清理空院校数据 ===");
  console.log("清理前recommend_list详细信息:");
  reportData.value.recommend_list.forEach((school, index) => {
    console.log(
      `  院校${index + 1}: "${
        school.school_name
      }" - 有内容: ${!!school.school_name}`
    );
  });

  // 过滤掉没有学校名称的空院校
  const originalLength = reportData.value.recommend_list.length;
  const originalList = [...reportData.value.recommend_list];

  reportData.value.recommend_list = reportData.value.recommend_list.filter(
    (school, index) => {
      // 更宽松的清理条件：除了检查学校名称，还检查是否有其他有意义的内容
      const hasSchoolName =
        school.school_name && school.school_name.trim() !== "";
      const hasDifficulty =
        school.difficulty_analysis && school.difficulty_analysis.trim() !== "";
      const hasSuggestion = school.suggest && school.suggest.trim() !== "";
      const hasAnyContent = hasSchoolName || hasDifficulty || hasSuggestion;

      console.log(
        `院校${index + 1}: "${
          school.school_name || "(空)"
        }" - 学校名: ${hasSchoolName}, 难度: ${hasDifficulty}, 建议: ${hasSuggestion} -> ${
          hasAnyContent ? "保留" : "移除"
        }`
      );
      return hasAnyContent;
    }
  );

  const cleanedLength = reportData.value.recommend_list.length;
  console.log(
    `清理结果: ${originalLength} -> ${cleanedLength} (移除了${
      originalLength - cleanedLength
    }个空院校)`
  );

  // 如果移除了所有院校，这可能是个问题
  if (cleanedLength === 0 && originalLength > 0) {
    console.warn("⚠️ 警告：清理后所有院校都被移除了，这可能不正常！");
    console.log("原始院校列表:", originalList);
    // 恢复原始数据以防万一
    reportData.value.recommend_list = originalList;
    console.log("已恢复原始院校数据");
  }

  console.log("=== 清理完成 ===");
};

// 提取并保存报告数据的函数
const extractAndSaveReportData = async () => {
  try {
    if (!currentReportId.value) {
      console.error("报告ID不存在");
      return;
    }

    // 提取school_list数据，按照指定格式
    const school_list = reportData.value.recommend_list
      .map((school, index) => {
        // 判断是否是高推荐院校
        const isHighRecommend = reportData.value.high_recommend_list?.some(
          (highSchool) => highSchool.school_name === school.school_name
        )
          ? 1
          : 0;

        return {
          school_id: Number(school.school_id) || 0,
          school_name: school.school_name || "",
          is_high_recommend: isHighRecommend,
          difficulty_analysis: school.difficulty_analysis || "",
          suggest: school.suggest || "",
          reason: school.reason || "",
          score_formula: school.basic_info?.score_formula || "",
          study_years: school.basic_info?.study_years || "",
          tuition_fee: school.basic_info?.tuition_fee || "",
          exam_subjects: school.basic_info?.exam_range || "",
          reference_books: school.basic_info?.reference_books || "",
          retest_content: school.basic_info?.retest_content || "",
          retest_score_requirement:
            school.basic_info?.retest_score_requirement || "",
          high_recommend_reason: isHighRecommend
            ? reportData.value.high_recommend_list?.[0]?.reason || ""
            : "",
          admission_requirements:
            school.basic_info?.admission_requirements || "",
        };
      })
      .filter((school) => school.school_name); // 过滤掉空的学校名称

    school_list.push({
      school_id: 0,
      school_name:
        reportData.value.high_recommend_list[0].school_name
          ?.split(",")[0]
          ?.split(":")[1] || "",
      is_high_recommend: 1,
      difficulty_analysis: "",
      suggest: "",
      reason: reportData.value.high_recommend_list[0].reason || "",
      score_formula: "",
      study_years: "",
      tuition_fee: "",
      exam_subjects: "",
      reference_books: "",
      retest_content: "",
      retest_score_requirement: "",
      high_recommend_reason:
        reportData.value.high_recommend_list[0].reason || "",
      admission_requirements: "",
    });

    const saveData = {
      report_id: currentReportId.value,
      school_list: school_list,
    };

    console.log("准备保存的报告数据:", saveData);

    // 调用API保存数据
    const response = await saveReportData(saveData);
    console.log("saveReport:", response);
    userstore.getUserInfo();
    if (response.data?.code === 0) {
      console.log("报告数据保存成功");
    } else {
      console.error("报告数据保存失败:", response.data?.msg);
    }
  } catch (error) {
    console.error("提取并保存报告数据失败:", error);
  }
};

// 重置数据方法
const resetData = (): void => {
  console.log("重置 content 组件数据");

  // 清空报告数据
  reportData.value = {
    recommend_list: [],
    high_recommend_list: [],
    school_list: [],
  } as AiRecommendationResult;

  // 重置编辑相关状态
  editingDifficulty.value = {};
  editingSuggestion.value = {};
  editDifficultyText.value = {};
  editSuggestionText.value = {};

  // 重置当前报告ID
  currentReportId.value = "";

  // 重置国家线数据
  nationalLineData.value = {
    subject_code: "",
    subject_name: "",
    years: [],
    a_total: [],
    a_single_100: [],
    a_single_over100: [],
    b_total: [],
    b_single_100: [],
    b_single_over100: [],
  };

  console.log("content 组件数据重置完成");
};

// 暴露方法给父组件
defineExpose({
  show,
  hide,
  toggle,
  isVisible,
  updateNationalLineData,
  setReportId,
  resetData,
  pdfUrl,
  currentReportId,
});

// 组件挂载
onMounted(() => {
  if (reportDataStore.hasReportData) {
    reportData.value = reportDataStore.reportData! as AiRecommendationResult;
  }
});
</script>

<style lang="less" scoped>
/* 步骤部分样式 */
.content-container {
  width: 100%;
  padding: 0 160px;
}

.step-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.step-header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/step-bg.png");
  background-repeat: no-repeat;
  padding-left: 92px;
  color: #fff;
  font-weight: bold;
}

.step-content {
  width: 100%;
  margin-top: 20px;
}

.step-num-tag {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-num-tag {
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitlebg.png");
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-num-tag span {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 50%;
  margin-right: 30px;
  font-weight: bold;
  padding-left: 13px;
}

.tag-text {
  color: #1bb394;
  font-weight: bold;
}

/* 学校表格样式 */
.school-table-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
}

.school-table-header {
  display: grid;
  grid-template-columns: 0.5fr 2fr 0.8fr 1fr 1fr 1fr 1fr 0.8fr 0.8fr;
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  height: 46px;
  line-height: 46px;
  text-align: center;
  padding: 0 10px;
}

.header-cell {
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.school-table-body {
  background-color: #fff;
}

.table-row {
  display: grid;
  grid-template-columns: 0.5fr 2fr 0.8fr 1fr 1fr 1fr 1fr 0.8fr 0.8fr;
  border-bottom: 1px solid #f0f0f0;
  height: 60px;
  line-height: 60px;
  text-align: center;
  padding: 0 10px;

  &:hover {
    background-color: #f5f7fa;
  }

  &:nth-child(even) {
    background-color: #f9f9f9;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.body-cell {
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #333;
}

.school-name {
  text-align: center;
  font-weight: bold;
  position: relative;
}

.school-tags {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.tag {
  display: inline-block;
  padding: 1px 6px;
  line-height: 1.5;
  font-size: 12px;
  border-radius: 3px;
  color: white;
  font-weight: normal;
}

.tag-985 {
  background-color: #ff9900;
}

.tag-211 {
  background-color: #8e6df8;
}

.tag-double {
  background-color: #1bb394;
}

/* 院校详情卡片样式 */
.school-detail-card {
  background-color: #fff;
  margin: 20px 0;
  overflow: hidden;
}

.school-header {
  display: flex;
  padding: 20px;
}

.school-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 90%;
    height: 90%;
    object-fit: contain;
  }
}

.school-info {
  flex: 1;
}

.school-title {
  display: flex;
  align-items: baseline;
  margin-bottom: 10px;

  h2 {
    font-size: 22px;
    color: #333;
    margin: 0;
    margin-right: 12px;
  }

  .school-location {
    font-size: 14px;
    color: #666;
  }
}

.school-tags-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .tag {
    margin-right: 0;
  }

  .major-diff {
    font-size: 14px;
    color: #333;
    margin-left: 5px;
  }
}

.section-title {
  font-size: 18px;
  margin: 15px 0;
  font-weight: 600;
  position: relative;
  padding-left: 15px;
}

.school-detail-section {
  margin-bottom: 20px;
  padding: 10px 20px 20px;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #2fc293;
}

.detail-item {
  display: flex;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.item-content {
  flex: 1;

  h4 {
    font-size: 16px;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin: 0 0 5px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 招生情况样式 */
.step-content-zs {
  width: 100%;
  margin-top: 20px;
}

.admission-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 20px 0 15px 0;
  position: relative;
  padding-left: 12px;
}

.admission-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
}

.admission-table th {
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  padding: 18px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
}

.admission-table td {
  padding: 18px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
  background-color: #fff;
}

.admission-table tr:nth-child(even) td {
  background-color: #f5fffd;
}
.admission-table,
.admission-table th,
.admission-table td {
  border: none !important;
}
.admission-table tr {
  border: 1px solid #e8f5f0 !important;
}
.reexam-container {
  border: 1px solid #1bb394;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}

.reexam-card {
  padding: 0 20px 20px;
}

.reexam-card:last-child {
  border-bottom: none;
}

.reexam-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;

  img {
    width: 27px;
    height: 21px;
    margin-right: 8px;
  }
}

.reexam-header .reexam-title {
  flex: 1;
  margin-left: 0;
}

.reexam-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.reexam-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.reexam-content p {
  margin: 8px 0;
}

.recommend-school-container-title {
  background-image: url(https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitlebg.png);
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: #1bb394;
  font-weight: bold;
}

/* 推荐综合性价比高的院校样式 */
.recommend-school-container {
  margin-top: 28px;
  border: 1px solid #1bb394;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
  background-color: #fff;
}

.recommend-school-card {
  padding: 0 20px 20px;
}

.recommend-school-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
}

.recommend-icon {
  margin-right: 8px;

  img {
    width: 27px;
    height: 21px;
  }
}

.recommend-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-left: 0;
}

.recommend-school-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.recommend-school-content p {
  margin: 8px 0;
}

/* 编辑功能样式 */
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-left: 8px;
}

.edit-btn {
  color: #1bb394 !important;
  font-size: 12px;
  padding: 0;
  border: none;
  background: none;
}

.edit-btn:hover {
  color: #2fc293 !important;
}

.regenerate-btn {
  color: #2fc293 !important;
  font-size: 12px;
  padding: 0;
  border: none;
  background: none;
}

.regenerate-btn:hover {
  color: #66b1ff !important;
}

.edit-container {
  margin-top: 10px;
}

.edit-textarea {
  margin-bottom: 10px;
}

.edit-textarea :deep(.el-textarea__inner) {
  border: 1px solid #ff4757 !important;
  box-shadow: 0 0 0 2px rgba(255, 71, 87, 0.2) !important;
  border-radius: 4px;
}

.edit-textarea :deep(.el-textarea__inner:focus) {
  border-color: #ff4757 !important;
}

.edit-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.save-btn {
  background-color: #1bb394 !important;
  border-color: #1bb394 !important;
  color: white !important;
}

.save-btn:hover {
  background-color: #2fc293 !important;
  border-color: #2fc293 !important;
}

/* 保存按钮样式 */
.save-report-container {
  display: flex;
  justify-content: center;
  padding: 30px 0;
  margin-top: 30px;
  border-top: 1px solid #e0e0e0;
}

.save-report-btn {
  padding: 12px 40px;
  font-size: 16px;
  font-weight: bold;
  background: linear-gradient(135deg, #1bb394, #2fc293);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(27, 179, 148, 0.3);
  transition: all 0.3s ease;
}

.save-report-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(27, 179, 148, 0.4);
}
</style>
