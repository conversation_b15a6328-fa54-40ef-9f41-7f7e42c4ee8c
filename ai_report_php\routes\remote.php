<?php
use <PERSON>man\Route;

 Route::get('/remote/regenerate_field', [app\controller\RemoteController::class, 'regenerateField']);
// 远程接口相关 - 需要鉴权
Route::group("/api", function () {
    // 消息处理
    Route::post('/remote/get_msg_str', [app\controller\RemoteController::class, 'getMsgStr']);
    
    // 学校和专业数据
    Route::post('/remote/school_and_major', [app\controller\RemoteController::class, 'schoolAndMajor']);
    
    // AI推荐相关
    Route::post('/remote/ai_recommendation', [app\controller\RemoteController::class, 'getAiRecommendation']);
    Route::get('/remote/stream_ai_recommendation', [app\controller\RemoteController::class, 'streamAiRecommendation']);
    Route::post('/remote/get_school_detail', [app\controller\RemoteController::class, 'getSchoolDetail']);
    Route::post('/remote/get_school_detail_new', [app\controller\SchoolDetailController::class, 'getSchoolDetail']);
    Route::post('/remote/get_school_overview', [app\controller\SchoolDetailController::class, 'getSchoolOverview']);
    Route::post('/api/report/detail', [app\controller\SchoolDetailController::class, 'getReportDetail']);
    Route::post('/remote/get_study_plan', [app\controller\RemoteController::class, 'getStudyPlan']);
    Route::post('/remote/get_study_plan_from_database', [app\controller\RemoteController::class, 'getStudyPlanFromDatabase']);

    // 学习计划编辑相关
    Route::post('/remote/update_weak_module_analysis', [app\controller\RemoteController::class, 'updateWeakModuleAnalysis']);
    Route::post('/remote/update_study_module', [app\controller\RemoteController::class, 'updateStudyModule']);
    Route::post('/remote/update_comprehensive_advice', [app\controller\RemoteController::class, 'updateComprehensiveAdvice']);

    // 目标分数相关
    Route::post('/remote/save_target_scores', [app\controller\RemoteController::class, 'saveTargetScores']);


    // 报告数据保存
    Route::post('/remote/save_report_data', [app\controller\RemoteController::class, 'saveReportData']);

    // 报告字段编辑保存
    Route::post('/remote/save_report_field_edit', [app\controller\RemoteController::class, 'saveReportFieldEdit']);

    // 字段重新生成（流式接口）
   

})->middleware([app\middleware\JwtMiddleware::class]);



